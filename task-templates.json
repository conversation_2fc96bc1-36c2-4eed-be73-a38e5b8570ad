{"templates": [{"id": "component-development", "name": "组件开发模板", "description": "用于新组件开发的标准模板", "category": "development", "priority": "medium", "estimatedHours": 8, "checklist": ["创建组件文件结构", "实现组件基础功能", "添加组件属性和事件", "编写组件文档", "添加单元测试", "集成到主项目"], "tags": ["component", "vue", "frontend"]}, {"id": "bug-fix", "name": "Bug修复模板", "description": "用于Bug修复的标准模板", "category": "development", "priority": "high", "estimatedHours": 2, "checklist": ["重现Bug", "分析问题原因", "实施修复方案", "编写测试用例", "验证修复效果", "更新相关文档"], "tags": ["bugfix", "maintenance"]}, {"id": "refactor-task", "name": "重构任务模板", "description": "用于代码重构的标准模板", "category": "refactor", "priority": "medium", "estimatedHours": 16, "checklist": ["分析现有代码结构", "设计重构方案", "实施重构", "更新测试用例", "性能测试", "文档更新"], "tags": ["refactor", "optimization"]}, {"id": "documentation", "name": "文档编写模板", "description": "用于文档编写的标准模板", "category": "documentation", "priority": "low", "estimatedHours": 4, "checklist": ["收集相关信息", "编写文档大纲", "撰写详细内容", "添加示例代码", "审核和校对", "发布文档"], "tags": ["documentation", "guide"]}, {"id": "testing-task", "name": "测试任务模板", "description": "用于测试相关任务的标准模板", "category": "testing", "priority": "high", "estimatedHours": 6, "checklist": ["分析测试需求", "设计测试用例", "编写测试代码", "执行测试", "分析测试结果", "优化测试覆盖率"], "tags": ["testing", "quality"]}]}