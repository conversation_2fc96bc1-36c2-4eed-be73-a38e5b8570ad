#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { v4: uuidv4 } = require('uuid');

class TaskManager {
  constructor() {
    this.tasksFile = path.join(process.cwd(), 'tasks.json');
    this.templatesFile = path.join(process.cwd(), 'task-templates.json');
    this.tasks = this.loadTasks();
    this.templates = this.loadTemplates();
  }

  // 加载任务数据
  loadTasks() {
    try {
      if (fs.existsSync(this.tasksFile)) {
        const data = fs.readFileSync(this.tasksFile, 'utf8');
        return JSON.parse(data);
      }
      return this.getDefaultTasksStructure();
    } catch (error) {
      console.error('加载任务文件失败:', error.message);
      return this.getDefaultTasksStructure();
    }
  }

  // 加载模板数据
  loadTemplates() {
    try {
      if (fs.existsSync(this.templatesFile)) {
        const data = fs.readFileSync(this.templatesFile, 'utf8');
        return JSON.parse(data);
      }
      return { templates: [] };
    } catch (error) {
      console.error('加载模板文件失败:', error.message);
      return { templates: [] };
    }
  }

  // 保存任务数据
  saveTasks() {
    try {
      // 更新元数据
      this.tasks.metadata.lastUpdated = new Date().toISOString().split('T')[0];
      this.tasks.metadata.totalTasks = this.tasks.tasks.length;
      this.tasks.metadata.completedTasks = this.tasks.tasks.filter(task => task.status === 'completed').length;
      
      fs.writeFileSync(this.tasksFile, JSON.stringify(this.tasks, null, 2), 'utf8');
      return true;
    } catch (error) {
      console.error('保存任务文件失败:', error.message);
      return false;
    }
  }

  // 获取默认任务结构
  getDefaultTasksStructure() {
    return {
      version: "1.0.0",
      metadata: {
        created: new Date().toISOString().split('T')[0],
        lastUpdated: new Date().toISOString().split('T')[0],
        totalTasks: 0,
        completedTasks: 0
      },
      categories: [],
      priorities: [],
      statuses: [],
      tasks: []
    };
  }

  // 创建新任务
  createTask(taskData) {
    const task = {
      id: uuidv4(),
      title: taskData.title || '未命名任务',
      description: taskData.description || '',
      category: taskData.category || 'development',
      priority: taskData.priority || 'medium',
      status: taskData.status || 'todo',
      tags: taskData.tags || [],
      assignee: taskData.assignee || '',
      estimatedHours: taskData.estimatedHours || 0,
      actualHours: 0,
      checklist: taskData.checklist || [],
      dependencies: taskData.dependencies || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      dueDate: taskData.dueDate || null,
      completedAt: null
    };

    this.tasks.tasks.push(task);
    this.saveTasks();
    return task;
  }

  // 更新任务
  updateTask(taskId, updates) {
    const taskIndex = this.tasks.tasks.findIndex(task => task.id === taskId);
    if (taskIndex === -1) {
      throw new Error(`任务 ${taskId} 不存在`);
    }

    const task = this.tasks.tasks[taskIndex];
    Object.assign(task, updates);
    task.updatedAt = new Date().toISOString();

    // 如果状态变为已完成，设置完成时间
    if (updates.status === 'completed' && task.status !== 'completed') {
      task.completedAt = new Date().toISOString();
    }

    this.saveTasks();
    return task;
  }

  // 删除任务
  deleteTask(taskId) {
    const taskIndex = this.tasks.tasks.findIndex(task => task.id === taskId);
    if (taskIndex === -1) {
      throw new Error(`任务 ${taskId} 不存在`);
    }

    const deletedTask = this.tasks.tasks.splice(taskIndex, 1)[0];
    this.saveTasks();
    return deletedTask;
  }

  // 获取任务列表
  getTasks(filters = {}) {
    let filteredTasks = [...this.tasks.tasks];

    // 按状态筛选
    if (filters.status) {
      filteredTasks = filteredTasks.filter(task => task.status === filters.status);
    }

    // 按分类筛选
    if (filters.category) {
      filteredTasks = filteredTasks.filter(task => task.category === filters.category);
    }

    // 按优先级筛选
    if (filters.priority) {
      filteredTasks = filteredTasks.filter(task => task.priority === filters.priority);
    }

    // 按标签筛选
    if (filters.tags && filters.tags.length > 0) {
      filteredTasks = filteredTasks.filter(task => 
        filters.tags.some(tag => task.tags.includes(tag))
      );
    }

    // 按负责人筛选
    if (filters.assignee) {
      filteredTasks = filteredTasks.filter(task => task.assignee === filters.assignee);
    }

    return filteredTasks;
  }

  // 获取单个任务
  getTask(taskId) {
    return this.tasks.tasks.find(task => task.id === taskId);
  }

  // 从模板创建任务
  createTaskFromTemplate(templateId, customData = {}) {
    const template = this.templates.templates.find(t => t.id === templateId);
    if (!template) {
      throw new Error(`模板 ${templateId} 不存在`);
    }

    const taskData = {
      title: customData.title || template.name,
      description: customData.description || template.description,
      category: customData.category || template.category,
      priority: customData.priority || template.priority,
      estimatedHours: customData.estimatedHours || template.estimatedHours,
      checklist: customData.checklist || [...template.checklist],
      tags: customData.tags || [...template.tags],
      ...customData
    };

    return this.createTask(taskData);
  }

  // 获取任务统计信息
  getStatistics() {
    const tasks = this.tasks.tasks;
    const stats = {
      total: tasks.length,
      byStatus: {},
      byCategory: {},
      byPriority: {},
      completionRate: 0,
      averageCompletionTime: 0
    };

    // 按状态统计
    this.tasks.statuses.forEach(status => {
      stats.byStatus[status.id] = tasks.filter(task => task.status === status.id).length;
    });

    // 按分类统计
    this.tasks.categories.forEach(category => {
      stats.byCategory[category.id] = tasks.filter(task => task.category === category.id).length;
    });

    // 按优先级统计
    this.tasks.priorities.forEach(priority => {
      stats.byPriority[priority.id] = tasks.filter(task => task.priority === priority.id).length;
    });

    // 完成率
    const completedTasks = tasks.filter(task => task.status === 'completed');
    stats.completionRate = tasks.length > 0 ? (completedTasks.length / tasks.length * 100).toFixed(2) : 0;

    // 平均完成时间（天）
    if (completedTasks.length > 0) {
      const totalDays = completedTasks.reduce((sum, task) => {
        if (task.completedAt && task.createdAt) {
          const created = new Date(task.createdAt);
          const completed = new Date(task.completedAt);
          return sum + (completed - created) / (1000 * 60 * 60 * 24);
        }
        return sum;
      }, 0);
      stats.averageCompletionTime = (totalDays / completedTasks.length).toFixed(2);
    }

    return stats;
  }
}

module.exports = TaskManager;

// 命令行接口
function runCLI() {
  const args = process.argv.slice(2);
  const command = args[0];
  const taskManager = new TaskManager();

  switch (command) {
    case 'list':
      listTasks(taskManager, args.slice(1));
      break;
    case 'create':
      createTaskCLI(taskManager, args.slice(1));
      break;
    case 'update':
      updateTaskCLI(taskManager, args.slice(1));
      break;
    case 'delete':
      deleteTaskCLI(taskManager, args.slice(1));
      break;
    case 'stats':
      showStatistics(taskManager);
      break;
    case 'help':
    default:
      showHelp();
      break;
  }
}

function listTasks(taskManager, args) {
  const filters = {};
  for (let i = 0; i < args.length; i += 2) {
    if (args[i] && args[i + 1]) {
      filters[args[i].replace('--', '')] = args[i + 1];
    }
  }

  const tasks = taskManager.getTasks(filters);
  console.log(`\n找到 ${tasks.length} 个任务:\n`);

  tasks.forEach(task => {
    console.log(`ID: ${task.id}`);
    console.log(`标题: ${task.title}`);
    console.log(`状态: ${task.status}`);
    console.log(`优先级: ${task.priority}`);
    console.log(`分类: ${task.category}`);
    console.log('---');
  });
}

function createTaskCLI(taskManager, args) {
  const title = args[0];
  if (!title) {
    console.error('请提供任务标题');
    return;
  }

  const task = taskManager.createTask({ title });
  console.log(`任务创建成功: ${task.id}`);
}

function updateTaskCLI(taskManager, args) {
  const taskId = args[0];
  const field = args[1];
  const value = args[2];

  if (!taskId || !field || !value) {
    console.error('用法: update <taskId> <field> <value>');
    return;
  }

  try {
    const updates = { [field]: value };
    const task = taskManager.updateTask(taskId, updates);
    console.log(`任务更新成功: ${task.title}`);
  } catch (error) {
    console.error('更新失败:', error.message);
  }
}

function deleteTaskCLI(taskManager, args) {
  const taskId = args[0];
  if (!taskId) {
    console.error('请提供任务ID');
    return;
  }

  try {
    const task = taskManager.deleteTask(taskId);
    console.log(`任务删除成功: ${task.title}`);
  } catch (error) {
    console.error('删除失败:', error.message);
  }
}

function showStatistics(taskManager) {
  const stats = taskManager.getStatistics();
  console.log('\n=== 任务统计 ===');
  console.log(`总任务数: ${stats.total}`);
  console.log(`完成率: ${stats.completionRate}%`);
  console.log(`平均完成时间: ${stats.averageCompletionTime} 天`);

  console.log('\n按状态统计:');
  Object.entries(stats.byStatus).forEach(([status, count]) => {
    console.log(`  ${status}: ${count}`);
  });

  console.log('\n按分类统计:');
  Object.entries(stats.byCategory).forEach(([category, count]) => {
    console.log(`  ${category}: ${count}`);
  });
}

function showHelp() {
  console.log(`
任务管理器 CLI

用法:
  node scripts/task-manager.js <command> [options]

命令:
  list [--status <status>] [--category <category>]  列出任务
  create <title>                                    创建任务
  update <taskId> <field> <value>                   更新任务
  delete <taskId>                                   删除任务
  stats                                             显示统计信息
  help                                              显示帮助信息

示例:
  node scripts/task-manager.js list --status todo
  node scripts/task-manager.js create "新功能开发"
  node scripts/task-manager.js update abc123 status completed
  `);
}

// 如果直接运行此脚本
if (require.main === module) {
  runCLI();
}
